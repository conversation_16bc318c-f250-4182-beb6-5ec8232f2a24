# postgres connection
PG_HOST=postgres
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# default database name
DEFAULT_DATABASE=revotraffic

# default login infomation
ADMIN_EMAIL=
ADMIN_PASSWORD=

# frontend will use these url and minio setting to call backend apis and download minio files
REACT_SERVER_URL=http://revo-server:7002
MINIO_ENDPOINT=
BUCKET_NAME=revotraffic
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin

# custom api for image and video job
IMAGE_WARP_API=http://***************:8000
VIDEO_JOB_API=http://**************:7100/api

# the host of this website
REACT_APP_URL=revo.punwave.com

# the host of minio
MINIO_CHELONIA_HOST=revo.storage.punwave.com
