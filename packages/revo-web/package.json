{"name": "revotraffic", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-free": "^6.3.0", "@fortawesome/fontawesome-svg-core": "^6.3.0", "@fortawesome/free-brands-svg-icons": "^6.3.0", "@fortawesome/free-regular-svg-icons": "^6.3.0", "@fortawesome/free-solid-svg-icons": "^6.3.0", "@fortawesome/react-fontawesome": "^0.2.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@visx/axis": "^3.5.0", "@visx/group": "^3.3.0", "@visx/legend": "^3.5.0", "@visx/mock-data": "^3.3.0", "@visx/responsive": "^3.3.0", "@visx/scale": "^3.5.0", "@visx/shape": "^3.5.0", "@visx/vendor": "^3.5.0", "axios": "^1.5.0", "bootstrap": "^5.3.0", "date-fns": "^2.30.0", "exceljs": "^4.3.0", "http-proxy-middleware": "^2.0.6", "moment": "^2.29.4", "prop-types": "^15.8.1", "react": "^18.2.0", "react-bootstrap": "^2.7.4", "react-date-range": "^1.4.0", "react-dom": "^18.2.0", "react-router-dom": "^6.13.0", "react-scripts": "5.0.1", "socket.io-client": "^4.7.2", "video-snapshot": "^1.0.11", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "startp": "yarn --cwd ./server install && yarn --cwd ./server start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint-config-airbnb": "^19.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "prettier": "^2.5.0"}}