import { useCallback, useContext } from 'react'
import { AuthContext } from '../components/ContextProvider'

export const Role = Object.freeze({
  SUPER_ADMIN: 'SUPER_ADMIN',
  USER: 'USER',
})

export const DraftUserRole = Object.freeze({
  SUPER_ADMIN: 'SUPER_ADMIN',
  PROJECT_ADMIN: 'PROJECT_ADMIN',
  PROJECT_DESIGNER: 'PROJECT_DESIGNER',
  VISITOR: 'VISITOR',
})

const projectPermissionMatrix = {
  [DraftUserRole.PROJECT_ADMIN]: {
    editMembers: true,
    editProject: true,
    viewProject: true,
    exportReport: true,
  },
  [DraftUserRole.PROJECT_DESIGNER]: {
    editMembers: false,
    editProject: true,
    viewProject: true,
    exportReport: true,
  },
  [DraftUserRole.VISITOR]: {
    editMembers: false,
    editProject: false,
    viewProject: true,
    exportReport: true,
  },
}

/**
 * A hook to check both global and project-specific permissions.
 * @param {string|boolean} projectRole - Role of the user within the current project
 * @returns {{
 *   canCreateProject: boolean,
 *   canAssignProjectAdmin: boolean,
 *   canEditMembers: boolean,
 *   canEditProject: boolean,
 *   canViewProject: boolean,
 *   canExportReport: boolean,
 *   hasPermission: function(string): boolean
 * }}
 */
export function usePermissions(projectRole) {
  const { auth: user } = useContext(AuthContext)
  const globalRole = user?.role || Role.USER

  // Global permissions granted only to SYSTEM_ADMIN
  const canCreateProject = globalRole === Role.SYSTEM_ADMIN
  const canAssignProjectAdmin = globalRole === Role.SYSTEM_ADMIN

  // Project-specific permissions
  const projectPerms = projectPermissionMatrix[projectRole] || {}

  return {
    canCreateProject,
    canAssignProjectAdmin,
    canEditMembers: Boolean(projectPerms.editMembers),
    canEditProject: Boolean(projectPerms.editProject),
    canViewProject: Boolean(projectPerms.viewProject),
    canExportReport: Boolean(projectPerms.exportReport),
    /**
     * Check any permission by its key
     * @param {string} action
     */
    hasPermission: (action) => {
      if (action === 'createProject') return canCreateProject
      if (action === 'assignProjectAdmin') return canAssignProjectAdmin
      return Boolean(projectPerms[action])
    },
  }
}

export default function useRoleAndPermission() {
  const { auth: user } = useContext(AuthContext)

  const checkPermission = useCallback(
    (permissions) =>
      user.role === Role.SUPER_ADMIN || permissions.includes(user.role),
    [user.role]
  )

  return { checkPermission }
}
