@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC&display=swap');

img {
  user-select: none
}

.App {
  text-align: center;
  width: 100vw;
  height: 100vh;
  font-family: 'Noto Sans TC', sans-serif;
}

.Nav {
  height: 8vh;
  max-height: 8vh;
  min-height: 8vh;
}

.oneLineEllipsis {
  display: block;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.textShadow {
  box-shadow: none;
  text-shadow: rgb(87, 179, 255) 0.1em 0.1em 0.2em;
  border-radius: 50%;
  cursor: grab;
}

.boxShadow {
  box-shadow: rgb(178, 178, 178) 0.1em 0.1em 0.2em;
}

.dropdown-item:active {
  background-color: #d2e4d5 !important;
}

.dropdown-item:hover {
  background-color: #d2e4d5 !important;
  cursor: pointer;
}

.breadcrumbs-two {
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
}

.breadcrumbs-two > li {
  float: left;
  width: 20%;
}

.breadcrumbs-two > li > a {
  display: flex;
  text-transform: uppercase;
  float: left;
  text-decoration: none;
  color: #FFF;
  position: relative;
  text-align: center;
  width: 100%;
  height: 60px;
  line-height: 1.3;
  font-size: 15px;
}

.breadcrumbs-two > li > a > span {
  display: inline-block;
  max-width: 100%;
  width: 150px;
  margin: auto;
  position: relative;
  color: white;
  font-size: 1.25rem;
  /* top: 20px; */
}

.breadcrumbs-two > li > a::before {
  content: "";
  position: absolute;
  top: 68%;
  margin-top: -41px;
  border-width: 30px 0 30px 30px;
  border-style: solid;
  border-color: #ddd #ddd #ddd transparent;
  left: -30px;
}

.breadcrumbs-two > li > a::after {
  content: "";
  position: absolute;
  top: 67%;
  margin-top: -40px;
  border-top: 40px solid transparent;
  border-bottom: 20px solid transparent;
  border-left: 30px solid #ddd;
  right: -30px;
}

.bread1 > a {
  background-color: #b9e473;
}

.bread1 > a::before {
  border-color: #b9e473 #b9e473 #b9e473 transparent !important;
}

.bread1 > a::after {
  border-color: transparent #b9e473 transparent #b9e473 !important;
}

.bread2 > a {
  background-color: #9fdd80;
}

.bread2 > a::before {
  border-color: #9fdd80 #9fdd80 #9fdd80 transparent !important;
}

.bread2 > a::after {
  border-color: transparent #9fdd80 transparent #9fdd80 !important;
}

.bread3 > a {
  background-color: #7fd48f;
}

.bread3 > a::before {
  border-color: #7fd48f #7fd48f #7fd48f transparent !important;
}

.bread3 > a::after {
  border-color: transparent #7fd48f transparent #7fd48f !important;
}

.bread4 > a {
  background-color: #63cb9d;
}

.bread4 > a::before {
  border-color: #63cb9d #63cb9d #63cb9d transparent !important;
}

.bread4 > a::after {
  border-color: transparent #1f7171 transparent #1f7171 !important;
}

.bread5 > a {
  background-color: #3ec3ab;
}

.bread5 > a::before {
  border-color: #3ec3ab #3ec3ab #3ec3ab transparent !important;
}

.bread1 > a::before {
  border-top: 0px !important;
  border-bottom: 0px !important;
  border-left: 0 !important;
}

.bread4 > a::after {
  right: -34px !important;
}

.bread5 > a::after {
  border-top: 0px !important;
  border-bottom: 0px !important;
  border-left: 0 !important;
}

.text-revo {
  color: #0e594f !important;
}

.text-revo-light {
  color: #698b87 !important;
}

.text-grey {
  color: #bdbdbd !important;
}

.check-revo {
  color: #68bc99 !important;
}

.btn-revo {
  color: #536a28 !important;
  background-color: #bde570 !important;
  border-color: #bde570 !important;
}

.btn-revo:hover, .btn-revo:active {
  color: #0e594f !important;
  background-color: #60ca9e !important;
  border-color: #60ca9e !important;
}

.btn-outline-revo {
  color: #536a28 !important;
  background-color: transparent !important;
  border-color: #536a28 !important;
}

.btn-outline-revo:hover, .btn-outline-revo:active {
  color: #fff !important;
  background-color: #a8c6bb !important;
  border-color: #a8c6bb !important;
}

.btn-revo2 {
  color: #093731 !important;
  background-color: #88d7b6 !important;
  border-color: #88d7b6 !important;
}

.btn-revo2:hover, .btn-revo2:active {
  color: #536a28 !important;
  background-color: #bde570 !important;
  border-color: #bde570 !important;
}

.btn-revo3 {
  color: #fff !important;
  background-color: #115c52 !important;
  border-color: #115c52 !important;
}

.btn-revo3:hover, .btn-revo2:active {
  color: #093731 !important;
  background-color: #88d7b6 !important;
  border-color: #88d7b6 !important;
}

.btn-outline-revo2 {
  color: #286a65 !important;
  background-color: transparent !important;
  border-color: #286a65 !important;
}

.btn-outline-revo2:hover, .btn-outline-revo2:active {
  color: #286a65 !important;
  background-color: #60ca9e !important;
  border-color: #60ca9e !important;
}

.btn-red {
  color: #49130c !important;
  background-color: #f6abab !important;
  border-color: #f6abab !important;
}

.btn-red:hover, .btn-red:active {
  color: #ffffff !important;
  background-color: #cb8383 !important;
  border-color: #cb8383 !important;
}

.btn-outline-red {
  color: #49130c !important;
  background-color: transparent !important;
  border-color: #49130c !important;
}

.btn-outline-red:hover, .btn-outline-red:active {
  color: #ffffff !important;
  background-color: #d1a4a4 !important;
  border-color: #d1a4a4 !important;
}

.bg-revo-linear {
  background: linear-gradient(to top, #38c2ab, #bae571) !important;
}

.bg-revo {
  background-color: #72cbae !important;
}

.bg-revo2 {
  background-color: #2e343a !important;
}

.bg-revo-mid {
  background-color: #e4e6e5 !important;
}

.bg-revo-light {
  background-color: #f4fcf8 !important;
}

.h-20 {
  height: 20% !important;
}

.h-35 {
  height: 35% !important;
}

.h-40 {
  height: 40% !important;
}

.h-70 {
  height: 70% !important;
}

.h-80 {
  height: 80% !important;
}

.h-82 {
  height: 82% !important;
}

.w-8 {
  width: 8% !important;
}

.w-15 {
  width: 15% !important;
}

.w-20 {
  width: 20% !important;
}

.w-30 {
  width: 30% !important;
}

.w-40 {
  width: 40% !important;
}

.w-60 {
  width: 60% !important;
}

.w-80 {
  width: 80% !important;
}

.modal-table > .container > .row > div:nth-child(2) > p {
  text-align: start !important;
}

.tooltip-inner {
  min-width: 45rem !important;
  width: 45rem !important;
}

.tooltip-inner > img {
  min-width: 40rem !important;
  width: 40rem !important;
}

.border-table-high {
  overflow: auto;
}

.border-table-high > .row {
  flex-wrap: nowrap;
  font-size: xx-small;
  text-align: center;
  height: 40px;
}

.border-table-high > .row > .col {
  text-align: center;
  padding: 0;
}

.border-table-high > .row > div > .row {
  text-align: center;
  justify-content: center;
  padding: 0;
}

.border-table {
  overflow: auto;
}

.border-table > .row {
  flex-wrap: nowrap;
  font-size: xx-small;
  text-align: center;
  height: 20px;
}

.border-table > .row > .col {
  text-align: center;
  padding: 0;
}

.border-table > .row > div > .row {
  text-align: center;
  justify-content: center;
  padding: 0;
}

/* .border-table > .row > * {
  border-top: 1px solid;
  border-bottom: 1px solid;
  border-left: 1px solid;
  border-right: 1px solid;
  padding: 0;
} */

.no-border-top {
  border-top: 0px solid;
}

.no-border-bottom {
  border-bottom: 0px solid;
}

.no-border-right {
  border-right: 0px solid;
}

.no-border-left {
  border-left: 0px solid;
}

.scrollbarShow::-webkit-scrollbar {
  width: .6em !important;
  height: 0em !important;
}

.scrollbarShow::-webkit-scrollbar-button {
  background: transparent !important;
}

.scrollbarShow::-webkit-scrollbar-track-piece {
  background: transparent !important;
}

.scrollbarShow::-webkit-scrollbar-track {
  box-shadow: inset 0px transparent !important;
}

.scrollbarShow::-webkit-scrollbar-thumb {
  background-color: #5d5275 !important;
  border-radius: 50rem !important;
}

.description > .tooltip-inner {
  width: 400px !important;
  max-width: 400px !important;
  min-width: 400px !important;
}

.wideModal > .modal-dialog {
  width: 90vw !important;
  max-width: 90vw !important;
  min-width: 90vw !important;
}