#!/usr/bin/env node
/* eslint-disable no-console */

/**
 * Module dependencies.
 */

const http = require('http')
const debug = require('debug')('d-server:server')
const app = require('../app')
const socket = require('../services/socket')

/**
 * Create HTTP server.
 */

const server = http.createServer(app);
socket.init(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
})

/**
 * Normalize a port into a number, string, or false.
 */

function normalizePort(val) {
  const port = parseInt(val, 10)

  if (Number.isNaN(port)) {
    // named pipe
    return val;
  }

  if (port >= 0) {
    // port number
    return port
  }

  return false
}

/**
 * Get port from environment and store in Express.
 */

const port = normalizePort(process.env.PORT || '7002');
app.set('port', port)

/**
 * Event listener for HTTP server "error" event.
 */

function onError(error) {
  if (error.syscall !== 'listen') {
    throw error
  }

  const bind = typeof port === 'string'
    ? `Pipe ${port}`
    : `Port ${port}`

  // handle specific listen errors with friendly messages
  switch (error.code) {
    case 'EACCES':
      console.error(`${bind} requires elevated privileges`)
      process.exit(1);
      break;
    case 'EADDRINUSE':
      console.error(`${bind} is already in use`)
      process.exit(1);
      break;
    default:
      throw error
  }
}

/**
 * Event listener for HTTP server "listening" event.
 */

function onListening() {
  const addr = server.address()
  const bind = typeof addr === 'string'
    ? `pipe ${addr}`
    : `port ${addr.port}`
  debug(`Listening on ${bind}`)
}

/**
 * set timeout
 */
server.setTimeout(60000)

/**
 * Listen on provided port, on all network interfaces.
 */

server.listen(port);
server.on('error', onError)
server.on('listening', onListening)
