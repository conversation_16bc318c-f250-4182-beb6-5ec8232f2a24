{"name": "d-server", "version": "0.0.0", "private": true, "scripts": {"start": "node ./bin/www"}, "dependencies": {"@aws-sdk/client-s3": "^3.409.0", "@aws-sdk/s3-request-presigner": "^3.409.0", "axios": "^0.26.1", "bcrypt": "^5.1.0", "cheerio": "^1.0.0-rc.12", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "csv-parse": "^5.3.5", "debug": "~2.6.9", "dotenv": "^10.0.0", "dumper.js": "^1.3.1", "ejs": "~2.6.1", "exceljs": "^4.3.0", "express": "~4.16.1", "express-http-proxy": "^2.0.0", "http-errors": "~1.6.3", "jsonwebtoken": "^9.0.1", "moment": "^2.29.2", "morgan": "~1.9.1", "multer": "^1.4.5-lts.1", "node-schedule": "^2.1.0", "nodemailer": "^6.7.2", "numeral": "^2.0.6", "openai": "4.0.0", "passport": "^0.6.0", "passport-local": "^1.0.0", "pg-promise": "^11.5.0", "socket.io": "^4.4.0"}, "devDependencies": {"eslint-config-airbnb": "^19.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "prettier": "^2.5.0"}}